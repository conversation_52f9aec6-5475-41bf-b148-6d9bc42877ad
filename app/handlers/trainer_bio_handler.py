import os
from typing import AsyncGenerator, List, Optional, Union

from ai_agent_utils.agent.performance_tracking import track_time
from ai_agent_utils.logging import logger
from ai_agent_utils.services.openai import OpenAIClient
from jinja2 import Environment, FileSystemLoader

from app.models.trainer_bio import TrainerBio
from app.repositories.trainer_bio import search_trainer_bio_by_name

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
env = Environment(loader=FileSystemLoader(f"{parent_dir}/templates"))


@track_time("ai-members-scheduling-agent", os.getenv("CHAT_DEPLOYMENT_NAME"))
async def trainer_bio_handler(
    question: str,
    history: list,
    scratch_pad: dict,
    extracted_trainer_name: Optional[str],
    stream: bool = False,
) -> Union[AsyncGenerator[str, None], dict]:
    logger.info(
        "Starting trainer_bio_handler",
        facets={
            "question": question,
            "extracted_trainer_name": extracted_trainer_name,
            "stream": stream,
        },
    )

    if not extracted_trainer_name:
        answer = "I couldn't quite catch the trainer's name you're asking about. Could you please tell me their name again? 😊"

        async def gen_missing_name_response():
            yield answer

        if stream:
            return {
                "answer": gen_missing_name_response(),
                "references": [],
                "path": "trainer_bio_query_name_missing",
                "metadata": None,
            }

        return {
            "answer": answer,
            "references": [],
            "path": "trainer_bio_query_name_missing",
            "metadata": None,
        }

    trainer_bios_data: Optional[List[TrainerBio]] = await search_trainer_bio_by_name(
        extracted_trainer_name
    )

    trainer_bios_renderable = None
    total_results_count = 0
    if trainer_bios_data:
        total_results_count = len(trainer_bios_data)
        limited_bios_data = trainer_bios_data[:10]
        trainer_bios_renderable = [bio.model_dump() for bio in limited_bios_data]

    template = env.get_template("trainer_bio_response.jinja2")
    system_prompt = template.render(
        question=question,
        history=history,
        trainer_bios=trainer_bios_renderable,
        requested_trainer_name=extracted_trainer_name,
        user_data=scratch_pad,
        total_results_count=total_results_count,
        results_were_limited=(total_results_count > 10),
    )

    openai_response = await OpenAIClient.chat_create(
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": question},
        ],
        stream=stream,
        temperature=0.1,
    )

    breakpoint()
    final_answer = openai_response
    if not stream and openai_response.choices:
        final_answer = openai_response.choices[0].message.content

    references = []

    return {
        "answer": final_answer,
        "references": references,
        "path": "trainer_bio_query_result",
        "metadata": None,
    }
